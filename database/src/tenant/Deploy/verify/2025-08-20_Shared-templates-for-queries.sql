-- Verify cm_tenant_db_DeploySchema:2025-08-20_Shared-templates-for-queries on pg

BEGIN;

DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'templates'
              AND column_name = 'shared'
        ) THEN
            RAISE EXCEPTION 'templates table is missing column: shared';
        END IF;
    END $$;

ROLLBACK;
