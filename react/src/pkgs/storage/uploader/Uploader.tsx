import { FileSelector } from '@/pkgs/storage/uploader/FileSelector'
import { useState } from 'react'
import { FileCard } from '@/pkgs/storage/uploader/FileCard'
import CMDialog from '@/common/components/CMDialog'
import { Button, DialogContent } from '@mui/material'

type UploaderProps = {
    type: 'image' | 'document'
    multiple: boolean
    onError?: (error: string, file: File) => void
    disabled?: boolean
    sizeLimit?: number
    buttonText?: string
}

export const Uploader = ({ type, multiple, onError, disabled, sizeLimit }: UploaderProps) => {
    const [files, setFiles] = useState<File[]>([])
    const onFileSelect = (file: File) => {
        setFiles((prev) => [...prev, file])
    }

    return (
        <div>
            <FileSelector type={type} multiple={multiple} onFileSelect={onFileSelect} sizeLimit={sizeLimit} />
            {files.map((file) => (
                <FileCard
                    key={file.name}
                    file={file}
                    type={type}
                    disabled={!!disabled}
                    onUpload={() => {}}
                    onDelete={() => {
                        setFiles(files.filter((f) => f.name !== file.name))
                    }}
                />
            ))}
        </div>
    )
}

type UploaderDialogProps = UploaderProps & {
    onClose: () => void
    open: boolean
}

export const UploaderDialog = ({ onClose, open, ...props }: UploaderDialogProps) => {
    const handleClose = (_e: unknown, reason: 'backdropClick' | 'escapeKeyDown') => {
        onClose()
    }

    return (
        <CMDialog open={open} onClose={handleClose} title='Upload' fullWidth maxWidth='lg'>
            <DialogContent>
                <Uploader {...props} />
            </DialogContent>
        </CMDialog>
    )
}

export const UploaderButton = ({ ...props }: UploaderProps) => {
    const [open, setOpen] = useState(false)

    return (
        <>
            <Button onClick={() => setOpen(true)}>Upload</Button>
            {open && <UploaderDialog {...props} open={open} onClose={() => setOpen(false)} />}
        </>
    )
}
