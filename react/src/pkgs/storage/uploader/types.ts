import { z } from 'zod'
import { entity, trackable } from '@/common/react-query'
import { base } from '@/pkgs/content/types'

export const fileSchema = z.object({
    ...entity.shape,
    ...base.shape,
    ...trackable.shape,
    Active: z.boolean(),

    StorageID: z.string(),
    FolderID: z.string(),


    ETag: z.string(),
    Hash: z.string(),
    Status: z.string(),

    Type: z.string(),
    ContentType: z.string(),
    Extension: z.string(),

    Filename: z.string(),
    Title: z.string(),
    Description: z.string(),
    Keywords: z.array(z.string()).nullish(),

    Tags: z.array(z.string()).nullish(),
    Meta: z.record(z.string(), z.string()).nullish(),
    StructureID: z.string().nullish(),
    Data: z.any().nullish(),

    FileSize: z.number(),
    Width: z.number().nullish(),
    Height: z.number().nullish()
})

export type FileType = z.infer<typeof fileSchema>
