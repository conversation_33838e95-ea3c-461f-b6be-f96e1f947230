import { z } from 'zod'
import { entity, paged, PagingQ<PERSON>y, SortingQuery, trackable, uuidSchema } from '@/common/react-query'
import { publishPeriod } from '@/pkgs/content/types'
import { keywordSearch } from '@/pkgs/search/promotions/types'

export type SuggestionsQuery = {
    Search?: string
    Inactive?: boolean
} & PagingQuery &
    SortingQuery

export const suggestionDto = z.object({
    ...keywordSearch.shape,
    ...publishPeriod.shape,
    Name: z.string(),
    Items: z.array(z.string())
})

export const suggestion = z.object({
    ...suggestionDto.shape,
    ...entity.shape,
    ...trackable.shape,
    Active: z.boolean()
})

export const searchSuggestionResult = paged.extend({
    Rows: z.array(suggestion)
})

export const suggestionDtoValidate = suggestionDto.extend({
    Keywords: z.array(z.string()).nonempty('Keywords must not be empty'),
    Name: z.string().min(3, 'Name must be at least 3 characters')
})

export type Suggestion = z.infer<typeof suggestion>
export type SuggestionDTO = z.infer<typeof suggestionDto>
