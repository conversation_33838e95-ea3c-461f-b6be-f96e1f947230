jobs:
  - job: build_react
    displayName: 'Build React'
    pool:
      vmImage: 'ubuntu-latest'
    variables:
      NODE_OPTIONS: --max_old_space_size=8192

    steps:
      - template: ../templates/setup.yml
        parameters:
          repositoryToCheck: 'cm-admin-ui'

      - template: ../templates/setup-go.yml
      - template: ../templates/setup-node.yml
        parameters:
          enableReactCache: true

      - script: |
          cd react
          npm ci --legacy-peer-deps
        condition: and(succeeded(), ne(variables.REACT_CACHE_RESTORED, 'true'))
        displayName: 'Install React dependencies'

      - script: |
          cd react
          npm run typecheck
        displayName: 'TypeScript type check'

      - script: |
          cd react
          npm run lint:check
        displayName: 'ESLint check'

      #      - script: |
      #          cd react
      #          npm run format:check
      #        displayName: 'Prettier format check'

      - script: |
          cd react
          CI=true npm run test:ci
        displayName: 'Run React tests'

      - script: |
          cd react
          BUILD_PATH="$(OUTPUT_DIR)/react" npm run build
        displayName: 'Run React build'

      - script: |
          cd go/servers/spa-server
          GO111MODULE=off CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o "$(OUTPUT_DIR)/spa-server" .
        displayName: 'Build SPA server'

      - template: ../templates/bash-script-template.yml
        parameters:
          scriptName: 'build-admin-ui.sh'

      - template: ../templates/list-output.yml
